const api = (path, opts = {}) => fetch(path, opts).then((r) => r.json());

async function loadTemplates() {
  const list = await api("/api/templates");
  const gallery = document.getElementById("gallery");
  gallery.innerHTML = "";
  for (const t of list) {
    const card = document.createElement("div");
    card.className = "card";

    const img = document.createElement("img");
    img.src = t.thumbnailUrl;
    img.alt = t.name;

    const title = document.createElement("div");
    title.className = "card-title";
    title.textContent = t.name;

    const btn = document.createElement("button");
    btn.textContent = "Use style";
    btn.onclick = () => openModal(t);

    card.appendChild(img);
    card.appendChild(title);
    card.appendChild(btn);
    gallery.appendChild(card);
  }
}

let currentTemplate = null;
function openModal(t) {
  currentTemplate = t;
  document.getElementById("modalTitle").textContent = t.name;
  document.getElementById("thumbPreview").src = t.thumbnailUrl;
  document.getElementById("modal").classList.remove("hidden");
  document.getElementById("resultArea").classList.add("hidden");
}

function closeModal() {
  document.getElementById("modal").classList.add("hidden");
}

document.getElementById("closeModal").addEventListener("click", closeModal);

document.getElementById("uploadForm").addEventListener("submit", async (e) => {
  e.preventDefault();
  if (!currentTemplate) return;
  const file = document.getElementById("imageInput").files[0];
  if (!file) return alert("Please choose an image");

  const fd = new FormData();
  fd.append("image", file);

  const res = await fetch(`/api/templates/${currentTemplate.id}/apply`, {
    method: "POST",
    body: fd,
  });
  const data = await res.json();
  if (data.resultUrl) {
    document.getElementById("resultImg").src = data.resultUrl;
    document.getElementById("downloadLink").href = data.resultUrl;
    document.getElementById("resultArea").classList.remove("hidden");
  } else {
    alert(data.error || "Failed to apply style");
  }
});

window.addEventListener("load", loadTemplates);
