AI Style Gallery - prototype

This repository contains a small prototype: an Express server that serves a static client gallery and a simple mock "AI" transform using Jim<PERSON>.

How it works

- Templates are stored in `server/templates.json` and include a `hiddenPrompt` field that is never returned by the API.
- The client lists templates and lets users upload an image to apply a selected style.
- The server applies a simple image processing pipeline to emulate a style.

Run (server requires Node/npm):

1. Open a terminal in the project root
2. Install server deps:

```powershell
cd "d:/NanoBanana Hacakthon/server"; npm install
```

3. Start server:

```powershell
node server/index.js
```

4. Open http://localhost:3000/

Notes

- This is a minimal prototype. Integrate a real AI image editing API (OpenAI, Stable Diffusion, Replicate, etc.) by replacing the transform in `server/index.js` and keeping the prompt hidden.
- Next steps: add authentication, database persistence, image CDN, pagination, rate-limiting, and usage analytics.

Hackathon submission checklist (Nano Banana)

- Video demo (<=2 minutes) showing the gallery, creating a template, and applying it to an image.
- Public project link (GitHub repo or live demo).
- Gemini integration write-up (max 200 words) — use the template below.

Gemini integration write-up (200 words max - template)

We integrated Gemini 2.5 Flash Image (Nano Banana) to power style-based image editing. Creators define a hidden template payload (stored as a private prompt on the server); when a user applies a style, the server sends the template's hidden prompt plus the user's uploaded image to Gemini's image-editing endpoint and returns the generated image. This keeps creators' prompts private while letting users apply polished styles with a single click. We implement a local Jimp fallback to ensure the demo runs offline or when no API key is available. Core features shown in the demo: template gallery, creator upload (hidden prompt stored server-side), and one-click image editing using Gemini. The demo focuses on usability—non-technical users pick styles and upload photos without writing prompts—while preserving creators' IP and enabling community sharing. For the hackathon submission, set GEMINI_API_KEY in the server environment to use the real Nano Banana API; otherwise the Jimp fallback provides a functional prototype.
