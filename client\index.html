<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>AI Style Gallery</title>
    <link rel="stylesheet" href="/client/styles.css" />
  </head>
  <body>
    <header>
      <div class="header-content">
        <div>
          <h1>AI Style Gallery</h1>
          <p>
            Pick a style, upload a photo, and let the AI edit it for you.
            Prompts are hidden — creators keep their magic private.
          </p>
        </div>
        <button id="createTemplateBtn" class="create-btn">
          Create Template
        </button>
      </div>
    </header>

    <main>
      <section id="gallery" class="grid"></section>
    </main>

    <!-- Apply Style Modal -->
    <div id="modal" class="modal hidden">
      <div class="modal-content">
        <button id="closeModal">×</button>
        <h2 id="modalTitle"></h2>
        <img id="thumbPreview" src="" alt="template preview" />
        <form id="uploadForm">
          <label for="imageInput">Upload your image:</label>
          <input
            type="file"
            id="imageInput"
            name="image"
            accept="image/*"
            required
          />
          <button type="submit">Apply style</button>
        </form>
        <div id="resultArea" class="hidden">
          <h3>Result</h3>
          <img id="resultImg" src="" alt="result" />
          <a id="downloadLink" href="#" download="result.jpg">Download</a>
        </div>
      </div>
    </div>

    <!-- Create Template Modal -->
    <div id="createModal" class="modal hidden">
      <div class="modal-content">
        <button id="closeCreateModal">×</button>
        <h2>Create New Template</h2>
        <form id="createTemplateForm">
          <div class="form-group">
            <label for="templateName">Template Name:</label>
            <input
              type="text"
              id="templateName"
              name="name"
              required
              placeholder="e.g., Cyberpunk Portrait"
            />
          </div>

          <div class="form-group">
            <label for="templateCreator">Your Name:</label>
            <input
              type="text"
              id="templateCreator"
              name="creator"
              required
              placeholder="Your creator name"
            />
          </div>

          <div class="form-group">
            <label for="templateStyle">Style Category:</label>
            <select id="templateStyle" name="style" required>
              <option value="">Select a category</option>
              <option value="cyberpunk">Cyberpunk</option>
              <option value="headshot">Professional Headshot</option>
              <option value="cartoon">Cartoon</option>
              <option value="artistic">Artistic</option>
              <option value="vintage">Vintage</option>
              <option value="fantasy">Fantasy</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div class="form-group">
            <label for="templatePrompt">AI Prompt (Hidden from users):</label>
            <textarea
              id="templatePrompt"
              name="hiddenPrompt"
              required
              placeholder="Describe the style transformation you want the AI to apply..."
              rows="4"
            ></textarea>
            <small
              >This prompt will be used by the AI but kept private from other
              users.</small
            >
          </div>

          <div class="form-group">
            <label for="templateThumbnail">Thumbnail Image:</label>
            <input
              type="file"
              id="templateThumbnail"
              name="thumbnail"
              accept="image/*"
              required
            />
            <small>Upload an example image showing the style effect.</small>
          </div>

          <div class="form-group">
            <label>
              <input type="checkbox" id="shareTemplate" name="shared" checked />
              Share with community
            </label>
          </div>

          <button type="submit">Create Template</button>
        </form>
      </div>
    </div>

    <script src="/client/app.js"></script>
  </body>
</html>
