<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>AI Style Gallery</title>
    <link rel="stylesheet" href="/client/styles.css" />
  </head>
  <body>
    <header>
      <h1>AI Style Gallery</h1>
      <p>
        Pick a style, upload a photo, and let the AI edit it for you. Prompts
        are hidden — creators keep their magic private.
      </p>
    </header>

    <main>
      <section id="gallery" class="grid"></section>
    </main>

    <div id="modal" class="modal hidden">
      <div class="modal-content">
        <button id="closeModal">×</button>
        <h2 id="modalTitle"></h2>
        <img id="thumbPreview" src="" alt="template preview" />
        <form id="uploadForm">
          <input
            type="file"
            id="imageInput"
            name="image"
            accept="image/*"
            required
          />
          <button type="submit">Apply style</button>
        </form>
        <div id="resultArea" class="hidden">
          <h3>Result</h3>
          <img id="resultImg" src="" alt="result" />
          <a id="downloadLink" href="#" download="result.jpg">Download</a>
        </div>
      </div>
    </div>

    <script src="/client/app.js"></script>
  </body>
</html>
