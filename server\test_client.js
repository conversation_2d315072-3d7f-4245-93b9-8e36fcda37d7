const fetch = require("node-fetch");
const fs = require("fs");
const path = require("path");

async function main() {
  const base = "http://localhost:3000";
  try {
    const res = await fetch(base + "/api/templates");
    const list = await res.json();
    console.log(
      "templates:",
      list.map((t) => ({ id: t.id, name: t.name }))
    );
  } catch (e) {
    console.error("failed to reach server", e.message);
  }
}

main();
