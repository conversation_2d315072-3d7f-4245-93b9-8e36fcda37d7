const express = require("express");
const cors = require("cors");
const multer = require("multer");
const fs = require("fs");
const path = require("path");
const Jimp = require("jimp");
const { v4: uuidv4 } = require("uuid");

// Optional Gemini client
let geminiClient = null;
try {
  const { GoogleGenAI } = require("@google/genai");
  if (process.env.GEMINI_API_KEY) {
    geminiClient = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    console.log("Gemini client initialized");
  }
} catch (e) {
  // package not installed or no api key; we'll use Jimp fallback
}

const app = express();
const PORT = process.env.PORT || 3000;

const BASE_DIR = __dirname;
const UPLOADS_DIR = path.join(BASE_DIR, "uploads");
const OUTPUTS_DIR = path.join(BASE_DIR, "outputs");
const TEMPLATES_FILE = path.join(BASE_DIR, "templates.json");

for (const d of [UPLOADS_DIR, OUTPUTS_DIR]) {
  if (!fs.existsSync(d)) fs.mkdirSync(d, { recursive: true });
}

app.use(cors());
app.use(express.json());
app.use("/client", express.static(path.join(BASE_DIR, "..", "client")));
app.use("/uploads", express.static(UPLOADS_DIR));
app.use("/outputs", express.static(OUTPUTS_DIR));

const storage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, UPLOADS_DIR),
  filename: (req, file, cb) =>
    cb(
      null,
      `${Date.now()}-${file.originalname.replace(/[^a-zA-Z0-9.]/g, "_")}`
    ),
});
const upload = multer({ storage });

function loadTemplates() {
  if (!fs.existsSync(TEMPLATES_FILE)) return [];
  try {
    return JSON.parse(fs.readFileSync(TEMPLATES_FILE));
  } catch (e) {
    return [];
  }
}
function saveTemplates(list) {
  fs.writeFileSync(TEMPLATES_FILE, JSON.stringify(list, null, 2));
}

// Return templates list but hide the hiddenPrompt
app.get("/api/templates", (req, res) => {
  const list = loadTemplates();
  const publicList = list.map((t) => ({
    id: t.id,
    name: t.name,
    thumbnailUrl: t.thumbnailUrl,
    creator: t.creator,
    shared: !!t.shared,
    style: t.style,
  }));
  res.json(publicList);
});

// Creator uploads a template; the hidden prompt is stored but never returned
app.post("/api/templates", upload.single("thumbnail"), (req, res) => {
  const {
    name,
    hiddenPrompt = "",
    style = "generic",
    creator = "unknown",
    shared = "true",
  } = req.body;
  if (!req.file)
    return res
      .status(400)
      .json({ error: "thumbnail image required (field: thumbnail)" });
  const templates = loadTemplates();
  const id = uuidv4();
  const thumbnailUrl = `/uploads/${path.basename(req.file.path)}`;
  const tpl = {
    id,
    name,
    thumbnailUrl,
    creator,
    shared: shared === "true",
    style,
    hiddenPrompt,
  };
  templates.push(tpl);
  saveTemplates(templates);
  res.json({ ok: true, id });
});

// Apply a template to an uploaded image (mock AI transform using simple Jimp operations)
app.post(
  "/api/templates/:id/apply",
  upload.single("image"),
  async (req, res) => {
    const id = req.params.id;
    if (!req.file)
      return res
        .status(400)
        .json({ error: "image file required (field: image)" });
    const templates = loadTemplates();
    const tpl = templates.find((t) => t.id === id && t.shared);
    if (!tpl)
      return res
        .status(404)
        .json({ error: "template not found or not shared" });

    const inputPath = req.file.path;
    // If we have a Gemini client and the creator kept a hidden prompt, call the Gemini API
    if (geminiClient && tpl.hiddenPrompt) {
      try {
        const base64 = fs.readFileSync(inputPath).toString("base64");
        const prompt = [
          { text: tpl.hiddenPrompt },
          { inlineData: { mimeType: "image/png", data: base64 } },
        ];

        const response = await geminiClient.models.generateContent({
          model: "gemini-2.5-flash-image-preview",
          contents: prompt,
        });
        const part = response.candidates?.[0]?.content?.parts?.find(
          (p) => p.inlineData
        );
        if (part && part.inlineData && part.inlineData.data) {
          const buffer = Buffer.from(part.inlineData.data, "base64");
          const outName = `${Date.now()}-${uuidv4()}.png`;
          const outPath = path.join(OUTPUTS_DIR, outName);
          fs.writeFileSync(outPath, buffer);
          return res.json({ resultUrl: `/outputs/${outName}` });
        }
        return res.status(500).json({ error: "gemini returned no image" });
      } catch (err) {
        console.error("Gemini processing failed:", err);
        // fall through to fallback
      }
    }

    // Fallback local transform using Jimp so the app works offline and without API keys
    try {
      let image = await Jimp.read(inputPath);
      // Very simple style emulation by style key
      if (tpl.style === "cyberpunk") {
        image = image
          .autocrop()
          .cover(1024, 1024)
          .color([{ apply: "desaturate", params: [10] }])
          .contrast(0.2);
        const overlay = new Jimp(
          image.bitmap.width,
          image.bitmap.height,
          "#220033"
        );
        overlay.opacity(0.25);
        image.composite(overlay, 0, 0);
        image.posterize(8);
      } else if (tpl.style === "headshot") {
        image = image.autocrop().contain(800, 800).blur(1).quality(90);
        // gentle brightness boost
        image.brightness(0.06).contrast(0.05);
      } else if (tpl.style === "cartoon") {
        image = image.resize(800, Jimp.AUTO).posterize(6).contrast(0.15);
        // edge-like effect by cloning and desaturating
        const edge = image.clone().greyscale().invert().contrast(0.5).blur(2);
        image.composite(edge, 0, 0);
      } else {
        // generic subtle filter
        image = image.resize(1024, Jimp.AUTO).brightness(0.03).contrast(0.02);
      }

      const outName = `${Date.now()}-${uuidv4()}.jpg`;
      const outPath = path.join(OUTPUTS_DIR, outName);
      await image.quality(85).writeAsync(outPath);

      // Return public URL
      res.json({ resultUrl: `/outputs/${outName}` });
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: "processing failed" });
    }
  }
);

// Serve client index at root
app.get("/", (req, res) => {
  res.sendFile(path.join(BASE_DIR, "..", "client", "index.html"));
});

app.listen(PORT, () =>
  console.log(`Server listening on http://localhost:${PORT}`)
);
