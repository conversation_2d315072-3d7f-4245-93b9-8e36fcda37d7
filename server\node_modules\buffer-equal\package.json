{"name": "buffer-equal", "description": "return whether two buffers are equal", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/substack/node-buffer-equal.git"}, "main": "index.js", "keywords": ["buffer", "equal"], "directories": {"example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "0.2.4"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}}