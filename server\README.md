Server for AI Style Gallery

Install:

```powershell
cd "d:/NanoBanana Hacakthon/server"; npm install
```

Run:

```powershell
node index.js
```

API:

- GET /api/templates -> list public templates (no hidden prompts)
- POST /api/templates (multipart form) -> create template (fields: name, style, hiddenPrompt, creator, shared=true, thumbnail file field name 'thumbnail')
- POST /api/templates/:id/apply (multipart form) -> apply style to uploaded image (file field 'image')

The server uses <PERSON><PERSON> for a mock transformation; replace with real AI calls later.
