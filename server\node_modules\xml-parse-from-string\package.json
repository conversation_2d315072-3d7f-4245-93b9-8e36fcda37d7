{"name": "xml-parse-from-string", "version": "1.0.1", "description": "DOMParser.parseFromString for XML with IE8 fallback", "main": "index.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "dependencies": {}, "devDependencies": {"brfs": "^1.4.0", "browserify": "^9.0.3", "faucet": "0.0.1", "tape": "^3.5.0", "testling": "^1.7.1"}, "scripts": {"test": "browserify test.js -t brfs | testling | faucet"}, "keywords": ["ie8", "fallback", "dom", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xml", "string", "parse", "browser", "browserify", "webpack", "activeXObject"], "repository": {"type": "git", "url": "git://github.com/Jam3/xml-parse-from-string.git"}, "homepage": "https://github.com/Jam3/xml-parse-from-string", "bugs": {"url": "https://github.com/Jam3/xml-parse-from-string/issues"}}