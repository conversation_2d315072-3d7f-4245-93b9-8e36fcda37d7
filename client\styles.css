* {
  box-sizing: border-box;
  font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
}
body {
  margin: 0;
  padding: 0;
  background: #0f1720;
  color: #e6eef8;
}
header {
  padding: 24px;
  background: linear-gradient(90deg, #0b1220, #112233);
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
}
header h1 {
  margin: 0;
  font-size: 28px;
}
header p {
  margin: 6px 0 0;
  color: #bcd0e6;
}
main {
  padding: 20px;
}
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}
.card {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.03),
    rgba(255, 255, 255, 0.01)
  );
  padding: 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.card img {
  width: 100%;
  height: 140px;
  object-fit: cover;
  border-radius: 6px;
  background: #223;
}
.card-title {
  margin-top: 8px;
  font-weight: 600;
}
.card button {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  border: 0;
  background: #3b82f6;
  color: white;
  cursor: pointer;
}
.modal {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(2, 6, 23, 0.6);
}
.modal.hidden {
  display: none;
}
.modal-content {
  background: #071027;
  padding: 18px;
  border-radius: 10px;
  max-width: 720px;
  width: 90%;
  position: relative;
}
#closeModal {
  position: absolute;
  right: 8px;
  top: 8px;
  background: transparent;
  border: 0;
  color: #dbeafe;
  font-size: 20px;
  cursor: pointer;
}
#thumbPreview {
  width: 100%;
  max-height: 240px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 12px;
}
#resultImg {
  width: 100%;
  max-height: 480px;
  object-fit: contain;
  border-radius: 6px;
}
#resultArea {
  margin-top: 12px;
}
