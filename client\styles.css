* {
  box-sizing: border-box;
  font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Aria<PERSON>, sans-serif;
}
body {
  margin: 0;
  padding: 0;
  background: #0f1720;
  color: #e6eef8;
}
header {
  padding: 24px;
  background: linear-gradient(90deg, #0b1220, #112233);
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

header h1 {
  margin: 0;
  font-size: 28px;
}
header p {
  margin: 6px 0 0;
  color: #bcd0e6;
}

.create-btn {
  padding: 12px 24px;
  border-radius: 8px;
  border: 0;
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.create-btn:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  transform: translateY(-1px);
}
main {
  padding: 20px;
}
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}
.card {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.03),
    rgba(255, 255, 255, 0.01)
  );
  padding: 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.card img {
  width: 100%;
  height: 140px;
  object-fit: cover;
  border-radius: 6px;
  background: #223;
}
.card-title {
  margin-top: 8px;
  font-weight: 600;
}
.card button {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  border: 0;
  background: #3b82f6;
  color: white;
  cursor: pointer;
}
.modal {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(2, 6, 23, 0.6);
}
.modal.hidden {
  display: none;
}
.modal-content {
  background: #071027;
  padding: 18px;
  border-radius: 10px;
  max-width: 720px;
  width: 90%;
  position: relative;
}
#closeModal {
  position: absolute;
  right: 8px;
  top: 8px;
  background: transparent;
  border: 0;
  color: #dbeafe;
  font-size: 20px;
  cursor: pointer;
}
#thumbPreview {
  width: 100%;
  max-height: 240px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 12px;
}
#resultImg {
  width: 100%;
  max-height: 480px;
  object-fit: contain;
  border-radius: 6px;
}
#resultArea {
  margin-top: 12px;
}

/* Loading states and animations */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner {
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Enhanced form styling */
form {
  margin-top: 16px;
}

form input[type="file"] {
  width: 100%;
  padding: 12px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  color: #e6eef8;
  margin-bottom: 12px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

form input[type="file"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
}

form button {
  width: 100%;
  padding: 12px 16px;
  border-radius: 8px;
  border: 0;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

form button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
}

form button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Enhanced card hover effects */
.card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.card:hover {
  transform: translateY(-4px);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.card button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Error and success states */
.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
  padding: 12px;
  border-radius: 6px;
  margin: 12px 0;
}

.success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #86efac;
  padding: 12px;
  border-radius: 6px;
  margin: 12px 0;
}

/* Template creator info */
.creator-info {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

/* Download button styling */
#downloadLink {
  display: inline-block;
  margin-top: 12px;
  padding: 8px 16px;
  background: #059669;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background 0.3s ease;
}

#downloadLink:hover {
  background: #047857;
}

/* Form styling */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #e6eef8;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  color: #e6eef8;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-group small {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #94a3b8;
}

.form-group input[type="checkbox"] {
  margin-right: 8px;
}

.form-group label:has(input[type="checkbox"]) {
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }

  .modal-content {
    width: 95%;
    padding: 16px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  header {
    padding: 16px;
  }

  header h1 {
    font-size: 24px;
  }

  .create-btn {
    width: 100%;
    max-width: 200px;
  }
}
