{"version": 3, "sources": ["../src/index.js"], "names": ["MIME_TYPE", "mime", "constants", "MIME_TIFF", "decoders", "data", "ifds", "UTIF", "decode", "page", "decodeImages", "rgba", "toRGBA8", "<PERSON><PERSON><PERSON>", "from", "width", "t256", "height", "t257", "encoders", "image", "tiff", "encodeImage", "bitmap"], "mappings": ";;;;;;;;;;;AAAA;;AAEA,IAAMA,SAAS,GAAG,YAAlB;;eAEe;AAAA,SAAO;AACpBC,IAAAA,IAAI,uCAAKD,SAAL,EAAiB,CAAC,MAAD,EAAS,KAAT,CAAjB,CADgB;AAGpBE,IAAAA,SAAS,EAAE;AACTC,MAAAA,SAAS,EAAEH;AADF,KAHS;AAOpBI,IAAAA,QAAQ,uCACLJ,SADK,EACO,UAACK,IAAD,EAAU;AACrB,UAAMC,IAAI,GAAGC,iBAAKC,MAAL,CAAYH,IAAZ,CAAb;;AACA,UAAMI,IAAI,GAAGH,IAAI,CAAC,CAAD,CAAjB;;AACAC,uBAAKG,YAAL,CAAkBL,IAAlB,EAAwBC,IAAxB;;AACA,UAAMK,IAAI,GAAGJ,iBAAKK,OAAL,CAAaH,IAAb,CAAb;;AAEA,aAAO;AACLJ,QAAAA,IAAI,EAAEQ,MAAM,CAACC,IAAP,CAAYH,IAAZ,CADD;AAELI,QAAAA,KAAK,EAAEN,IAAI,CAACO,IAAL,CAAU,CAAV,CAFF;AAGLC,QAAAA,MAAM,EAAER,IAAI,CAACS,IAAL,CAAU,CAAV;AAHH,OAAP;AAKD,KAZK,CAPY;AAsBpBC,IAAAA,QAAQ,uCACLnB,SADK,EACO,UAACoB,KAAD,EAAW;AACtB,UAAMC,IAAI,GAAGd,iBAAKe,WAAL,CACXF,KAAK,CAACG,MAAN,CAAalB,IADF,EAEXe,KAAK,CAACG,MAAN,CAAaR,KAFF,EAGXK,KAAK,CAACG,MAAN,CAAaN,MAHF,CAAb;;AAMA,aAAOJ,MAAM,CAACC,IAAP,CAAYO,IAAZ,CAAP;AACD,KATK;AAtBY,GAAP;AAAA,C", "sourcesContent": ["import UTIF from \"utif\";\n\nconst MIME_TYPE = \"image/tiff\";\n\nexport default () => ({\n  mime: { [MIME_TYPE]: [\"tiff\", \"tif\"] },\n\n  constants: {\n    MIME_TIFF: MIME_TYPE,\n  },\n\n  decoders: {\n    [MIME_TYPE]: (data) => {\n      const ifds = UTIF.decode(data);\n      const page = ifds[0];\n      UTIF.decodeImages(data, ifds);\n      const rgba = UTIF.toRGBA8(page);\n\n      return {\n        data: Buffer.from(rgba),\n        width: page.t256[0],\n        height: page.t257[0],\n      };\n    },\n  },\n\n  encoders: {\n    [MIME_TYPE]: (image) => {\n      const tiff = UTIF.encodeImage(\n        image.bitmap.data,\n        image.bitmap.width,\n        image.bitmap.height\n      );\n\n      return Buffer.from(tiff);\n    },\n  },\n});\n"], "file": "index.js"}